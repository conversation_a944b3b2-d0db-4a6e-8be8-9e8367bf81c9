import React, { FC } from 'react';
import './Modal.scss';
import SecondaryButton from '../SecondaryButton/SecondaryButton';
import IconButton from '../IconButton/IconButton';
import PrimaryButton from '../PrimaryButton/PrimaryButton';


export interface ModalProps {
	onCancel?: () => void;
	onConfirm?: () => void;
	onClose?: () => void;
	cancelText?: string;
	confirmText?: string;
	isConfirmDisabled?: boolean;
	title?: string;
	body?: string | React.ReactNode;
	contentChildren?: React.ReactNode;
	id?: string;
    className?: string
}

const Modal: FC<ModalProps> = ({
	onCancel,
	onConfirm,
    onClose,
	isConfirmDisabled = false,
	title,
	body,
	contentChildren,
	id,
	cancelText = 'Cancelar',
	confirmText = 'Aceptar',
    className
}) => {
	return (
		<div className={`${className ?? ''} modal-fullscreen-wrapper`} id={id} role="dialog" aria-modal="true">
			<div className="modal-overlay" onClick={onCancel} />
			<div className={`modal-wrapper ${onClose ? 'top-padding' : ''}`}>

				{/* Close */}
				{onClose && (
					<IconButton
						onClick={onClose}
						size="big"
                        state="default"
						iconType="close"
						backgroundColor="transparent"
						ariaLabel="Cerrar modal"
                        className='button-close'
					/>
				)}

				{/* Content */}
				<div className={`modal-content`}>
                    <div className='modal-header'>
                        <h2 className="modal-title title2 bold">{title}</h2>
                        {body && (
                            typeof body === 'string' ? (
                                <p className="modal-body body1">{body}</p>
                            ) : (
                                <div className="modal-body body1">{body}</div>
                            )
                        )}
                    </div>

					{contentChildren}
				</div>

				<div className="modal-footer">
					{ onCancel && <SecondaryButton onClick={onCancel} text={cancelText}/>}
					{ onConfirm && <PrimaryButton isDisabled={isConfirmDisabled} onClick={onConfirm} text={confirmText}/>}
				</div>
			</div>
		</div>
	);
};

export default Modal;
